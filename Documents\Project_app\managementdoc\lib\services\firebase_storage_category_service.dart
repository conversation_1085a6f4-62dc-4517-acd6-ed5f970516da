import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import '../core/services/firebase_service.dart';

/// Service for managing Firebase Storage category folder structure
class FirebaseStorageCategoryService {
  final FirebaseService _firebaseService = FirebaseService.instance;

  /// Create a category folder in Firebase Storage
  Future<void> createCategoryFolder(
    String categoryId,
    String categoryName,
  ) async {
    try {
      // Sanitize category name for folder structure
      final sanitizedName = _sanitizeFolderName(categoryName);
      final folderPath = 'documents/$categoryId-$sanitizedName';

      // Create a placeholder file to ensure the folder exists
      final placeholderRef = _firebaseService.storage
          .ref()
          .child(folderPath)
          .child('.folder_placeholder');

      // Upload a small placeholder file
      await placeholderRef.putString(
        'This folder was created for category: $categoryName',
        metadata: SettableMetadata(
          contentType: 'text/plain',
          customMetadata: {
            'categoryId': categoryId,
            'categoryName': categoryName,
            'createdAt': DateTime.now().millisecondsSinceEpoch.toString(),
            'type': 'folder_placeholder',
          },
        ),
      );

      debugPrint('✅ Created category folder: $folderPath');
    } catch (e) {
      debugPrint('❌ Failed to create category folder: $e');
      rethrow;
    }
  }

  /// Delete a category folder and all its contents
  Future<void> deleteCategoryFolder(
    String categoryId,
    String categoryName,
  ) async {
    try {
      final sanitizedName = _sanitizeFolderName(categoryName);
      final folderPath = 'documents/$categoryId-$sanitizedName';

      // List all files in the category folder
      final folderRef = _firebaseService.storage.ref().child(folderPath);
      final listResult = await folderRef.listAll();

      // Delete all files in the folder
      for (final item in listResult.items) {
        await item.delete();
        debugPrint('🗑️ Deleted file: ${item.name}');
      }

      debugPrint('✅ Deleted category folder: $folderPath');
    } catch (e) {
      debugPrint('❌ Failed to delete category folder: $e');
      rethrow;
    }
  }

  /// Move file to category folder
  Future<String> moveFileToCategory(
    String currentFilePath,
    String categoryId,
    String categoryName,
    String fileName,
  ) async {
    try {
      // Get reference to current file
      final currentRef = _firebaseService.storage.ref().child(currentFilePath);

      // Get file data and metadata
      final fileData = await currentRef.getData();
      final metadata = await currentRef.getMetadata();

      if (fileData == null) {
        throw Exception('File data not found');
      }

      // Create new path in category folder
      final sanitizedName = _sanitizeFolderName(categoryName);
      final newPath = 'documents/$categoryId-$sanitizedName/$fileName';
      final newRef = _firebaseService.storage.ref().child(newPath);

      // Upload file to new location with updated metadata
      final updatedMetadata = SettableMetadata(
        contentType: metadata.contentType,
        customMetadata: {
          ...metadata.customMetadata ?? {},
          'categoryId': categoryId,
          'categoryName': categoryName,
          'movedAt': DateTime.now().millisecondsSinceEpoch.toString(),
        },
      );

      await newRef.putData(fileData, updatedMetadata);

      // Delete original file
      await currentRef.delete();

      debugPrint('✅ Moved file from $currentFilePath to $newPath');
      return newPath;
    } catch (e) {
      debugPrint('❌ Failed to move file to category: $e');
      rethrow;
    }
  }

  /// Get category folder path
  String getCategoryFolderPath(String categoryId, String categoryName) {
    final sanitizedName = _sanitizeFolderName(categoryName);
    return 'documents/$categoryId-$sanitizedName';
  }

  /// List all files in a category folder
  Future<List<Reference>> listCategoryFiles(
    String categoryId,
    String categoryName,
  ) async {
    try {
      final folderPath = getCategoryFolderPath(categoryId, categoryName);
      final folderRef = _firebaseService.storage.ref().child(folderPath);
      final listResult = await folderRef.listAll();

      // Filter out placeholder files
      return listResult.items
          .where((ref) => !ref.name.startsWith('.'))
          .toList();
    } catch (e) {
      debugPrint('❌ Failed to list category files: $e');
      return [];
    }
  }

  /// Check if category folder exists
  Future<bool> categoryFolderExists(
    String categoryId,
    String categoryName,
  ) async {
    try {
      final folderPath = getCategoryFolderPath(categoryId, categoryName);
      final placeholderRef = _firebaseService.storage
          .ref()
          .child(folderPath)
          .child('.folder_placeholder');

      await placeholderRef.getMetadata();
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Sanitize folder name for Firebase Storage
  String _sanitizeFolderName(String name) {
    // Remove special characters and replace spaces with underscores
    return name
        .replaceAll(RegExp(r'[^\w\s-]'), '')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  /// Get upload path for new files in category
  String getUploadPath(
    String categoryId,
    String categoryName,
    String fileName,
  ) {
    final sanitizedName = _sanitizeFolderName(categoryName);
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final sanitizedFileName = _sanitizeFileName(fileName);
    return 'documents/$categoryId-$sanitizedName/${timestamp}_$sanitizedFileName';
  }

  /// Get upload path for uncategorized files
  String getUncategorizedUploadPath(String fileName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final sanitizedFileName = _sanitizeFileName(fileName);
    return 'documents/uncategorized/${timestamp}_$sanitizedFileName';
  }

  /// Sanitize file name
  String _sanitizeFileName(String fileName) {
    // Keep original extension but sanitize the name part
    final parts = fileName.split('.');
    final extension = parts.length > 1 ? parts.last : '';
    final nameWithoutExt = parts.length > 1
        ? parts.sublist(0, parts.length - 1).join('.')
        : fileName;

    final sanitizedName = nameWithoutExt
        .replaceAll(RegExp(r'[^\w\s.-]'), '')
        .replaceAll(RegExp(r'\s+'), '_');

    return extension.isNotEmpty ? '$sanitizedName.$extension' : sanitizedName;
  }

  /// Migrate existing files to category structure
  Future<void> migrateExistingFilesToCategories() async {
    try {
      debugPrint(
        '🔄 Starting migration of existing files to category structure...',
      );

      // Get all files in the documents folder
      final documentsRef = _firebaseService.storage.ref().child('documents');
      final listResult = await documentsRef.listAll();

      // Create uncategorized folder for files without category
      await _createUncategorizedFolder();

      int migratedCount = 0;
      for (final fileRef in listResult.items) {
        try {
          // Skip files already in subfolders
          if (fileRef.fullPath.split('/').length > 2) continue;

          // Get file metadata to check category
          final metadata = await fileRef.getMetadata();
          final categoryId = metadata.customMetadata?['categoryId'];

          if (categoryId == null || categoryId == 'uncategorized') {
            // Move to uncategorized folder
            await _moveToUncategorizedFolder(fileRef);
          }

          migratedCount++;
        } catch (e) {
          debugPrint('⚠️ Failed to migrate file ${fileRef.name}: $e');
        }
      }

      debugPrint('✅ Migration completed. Migrated $migratedCount files.');
    } catch (e) {
      debugPrint('❌ Migration failed: $e');
      rethrow;
    }
  }

  /// Create uncategorized folder
  Future<void> _createUncategorizedFolder() async {
    try {
      final placeholderRef = _firebaseService.storage.ref().child(
        'documents/uncategorized/.folder_placeholder',
      );

      await placeholderRef.putString(
        'This folder contains uncategorized files',
        metadata: SettableMetadata(
          contentType: 'text/plain',
          customMetadata: {
            'type': 'uncategorized_folder',
            'createdAt': DateTime.now().millisecondsSinceEpoch.toString(),
          },
        ),
      );
    } catch (e) {
      debugPrint('❌ Failed to create uncategorized folder: $e');
    }
  }

  /// Move file to uncategorized folder
  Future<void> _moveToUncategorizedFolder(Reference fileRef) async {
    try {
      final fileData = await fileRef.getData();
      final fullMetadata = await fileRef.getMetadata();

      if (fileData == null) return;

      final newPath = 'documents/uncategorized/${fileRef.name}';
      final newRef = _firebaseService.storage.ref().child(newPath);

      // Convert FullMetadata to SettableMetadata
      final settableMetadata = SettableMetadata(
        contentType: fullMetadata.contentType,
        customMetadata: fullMetadata.customMetadata,
      );

      await newRef.putData(fileData, settableMetadata);
      await fileRef.delete();

      debugPrint('📁 Moved ${fileRef.name} to uncategorized folder');
    } catch (e) {
      debugPrint('❌ Failed to move ${fileRef.name} to uncategorized: $e');
    }
  }
}
