import 'dart:async';
import 'dart:typed_data';
import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:file_selector/file_selector.dart';
import '../models/upload_file_model.dart';
import '../models/document_model.dart';
import '../core/services/firebase_service.dart';
import '../core/utils/anr_prevention.dart';
import 'firebase_storage_category_service.dart';

class FileUploadService {
  final FirebaseService _firebaseService = FirebaseService.instance;
  final FirebaseStorageCategoryService _categoryStorageService =
      FirebaseStorageCategoryService();
  final Map<String, StreamSubscription> _uploadSubscriptions = {};
  final Map<String, UploadTask?> _uploadTasks = {};

  // Check Firebase connectivity before upload
  Future<bool> checkConnectivity() async {
    try {
      // Check authentication first
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        return false;
      }

      // Try to list files in the documents folder to test access
      final documentsRef = _firebaseService.storage.ref().child('documents');
      await documentsRef.listAll().timeout(const Duration(seconds: 10));
      return true;
    } catch (e) {
      // If documents folder doesn't exist, try to create a test file
      try {
        final testRef = _firebaseService.storage.ref().child('documents/.keep');
        await testRef.putString('test').timeout(const Duration(seconds: 10));
        return true;
      } catch (createError) {
        return false;
      }
    }
  }

  // Upload file to Firebase Storage and save metadata to Firestore
  Future<String> uploadFile(
    UploadFileModel file, {
    required Function(double) onProgress,
  }) async {
    try {
      // Get current user and ensure authentication
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Pre-check file size before reading to prevent ANR
      final fileSize = await file.file.length();
      if (fileSize > 15 * 1024 * 1024) {
        throw Exception('File too large - maximum size is 15MB');
      }

      // Use chunked reading for large files to prevent ANR
      final bytes = await _readFileWithChunking(
        file.file,
        fileSize,
        onProgress,
      );

      if (bytes == null) {
        throw Exception(
          'Failed to read file - operation timed out or cancelled',
        );
      }

      // Validate file size (max 15MB to match Firebase Storage rules)
      const maxFileSize = 15 * 1024 * 1024; // 15MB
      if (bytes.length > maxFileSize) {
        throw Exception('File size exceeds 15MB limit');
      }

      // Create storage reference with category-based path
      final sanitizedFileName = _sanitizeFileName(file.fileName);
      String storagePath;

      if (file.categoryId != null && file.categoryId != 'uncategorized') {
        // Use category-specific folder path
        storagePath = _categoryStorageService.getUploadPath(
          file.categoryId!,
          'category', // Default category name, will be updated when we have category details
          sanitizedFileName,
        );
      } else {
        // Use uncategorized folder path
        storagePath = _categoryStorageService.getUncategorizedUploadPath(
          sanitizedFileName,
        );
      }

      final storageRef = _firebaseService.storage.ref().child(storagePath);

      // Determine proper content type
      final contentType = _getContentType(file.fileName, file.fileType);

      // Create upload task with proper metadata
      final uploadTask = storageRef.putData(
        bytes,
        SettableMetadata(
          contentType: contentType,
          customMetadata: {
            'originalName': file.fileName,
            'uploadedBy': currentUser.uid,
            'fileSize': bytes.length.toString(),
            'categoryId': file.categoryId ?? 'uncategorized',
            'uploadTimestamp': DateTime.now().millisecondsSinceEpoch.toString(),
          },
        ),
      );

      // Store upload task for pause/cancel functionality
      _uploadTasks[file.id] = uploadTask;

      // Listen to upload progress with better error handling
      final subscription = uploadTask.snapshotEvents.listen(
        (TaskSnapshot snapshot) {
          if (snapshot.state == TaskState.running) {
            final progress =
                (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            onProgress(progress);
          }
        },
        onError: (error) {
          // Handle specific Firebase Storage errors
          if (error is FirebaseException) {
            switch (error.code) {
              case 'cancelled':
                throw Exception('Upload cancelled by user');
              case 'unauthenticated':
                throw Exception('Authentication required - please login again');
              case 'unauthorized':
                throw Exception('Permission denied - check access rights');
              case 'retry-limit-exceeded':
                throw Exception('Network error - please try again');
              case 'invalid-checksum':
                throw Exception('File corrupted during upload');
              case 'quota-exceeded':
                throw Exception('Storage quota exceeded');
              default:
                throw Exception('Upload failed: ${error.message}');
            }
          } else {
            throw Exception('Upload failed: ${error.toString()}');
          }
        },
      );

      _uploadSubscriptions[file.id] = subscription;

      // Wait for upload to complete with reduced timeout for better UX
      final snapshot = await uploadTask.timeout(
        const Duration(
          minutes: 3,
        ), // Reduced to 3 minutes for faster failure detection
        onTimeout: () {
          uploadTask.cancel();
          throw Exception('Upload timeout - file too large or slow connection');
        },
      );

      final downloadUrl = await snapshot.ref.getDownloadURL();

      // Save document metadata to Firestore
      await _saveDocumentMetadata(file, downloadUrl, snapshot.ref.fullPath);

      // Clean up
      _cleanup(file.id);

      return downloadUrl;
    } catch (e) {
      _cleanup(file.id);
      rethrow;
    }
  }

  // Save document metadata to Firestore
  Future<void> _saveDocumentMetadata(
    UploadFileModel file,
    String downloadUrl,
    String storagePath,
  ) async {
    // Get current user
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }

    final document = DocumentModel(
      id: file.id,
      fileName: file.fileName,
      fileSize: file.fileSize,
      fileType: file.fileType,
      filePath: storagePath,
      uploadedBy: currentUser.uid,
      uploadedAt: DateTime.now(),
      category:
          file.categoryId ?? 'uncategorized', // Use the actual category ID
      status: 'pending', // Default status for new uploads
      permissions: [currentUser.uid], // User has access to their own uploads
      metadata: DocumentMetadata(
        description: 'Uploaded via mobile app',
        tags: _generateTags(file.fileName),
      ),
    );

    await _firebaseService.documentsCollection
        .doc(file.id)
        .set(document.toMap());
  }

  // Generate tags based on filename
  List<String> _generateTags(String fileName) {
    final tags = <String>[];
    final nameParts = fileName
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s]'), ' ')
        .split(' ')
        .where((part) => part.isNotEmpty && part.length > 2)
        .toList();

    tags.addAll(nameParts);

    // Add file type tag
    final extension = fileName.split('.').last.toLowerCase();
    tags.add(extension);

    return tags.take(10).toList(); // Limit to 10 tags
  }

  // Sanitize filename for Firebase Storage
  String _sanitizeFileName(String fileName) {
    // Remove or replace invalid characters for Firebase Storage
    return fileName
        .replaceAll(RegExp(r'[^\w\s\-\.]'), '_')
        .replaceAll(RegExp(r'\s+'), '_')
        .toLowerCase();
  }

  // Get proper content type based on file extension
  String _getContentType(String fileName, String fallbackType) {
    final extension = fileName.split('.').last.toLowerCase();

    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'pdf':
        return 'application/pdf';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case 'ppt':
        return 'application/vnd.ms-powerpoint';
      case 'pptx':
        return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
      case 'txt':
        return 'text/plain';
      default:
        return fallbackType.isNotEmpty
            ? fallbackType
            : 'application/octet-stream';
    }
  }

  // Pause upload
  void pauseUpload(String fileId) {
    final uploadTask = _uploadTasks[fileId];
    uploadTask?.pause();
  }

  // Resume upload
  void resumeUpload(String fileId) {
    final uploadTask = _uploadTasks[fileId];
    uploadTask?.resume();
  }

  // Cancel upload
  void cancelUpload(String fileId) {
    final uploadTask = _uploadTasks[fileId];
    uploadTask?.cancel();
    _cleanup(fileId);
  }

  // Clean up resources
  void _cleanup(String fileId) {
    _uploadSubscriptions[fileId]?.cancel();
    _uploadSubscriptions.remove(fileId);
    _uploadTasks.remove(fileId);
  }

  // Get upload statistics
  Map<String, dynamic> getUploadStats() {
    return {
      'activeUploads': _uploadTasks.length,
      'totalSubscriptions': _uploadSubscriptions.length,
    };
  }

  /// Read file with chunking to prevent ANR for large files
  Future<Uint8List?> _readFileWithChunking(
    XFile file,
    int fileSize,
    Function(double) onProgress,
  ) async {
    try {
      // For small files (< 5MB), read normally
      if (fileSize < 5 * 1024 * 1024) {
        return await ANRPrevention.executeWithTimeout(
          file.readAsBytes(),
          timeout: const Duration(seconds: 10),
          operationName: 'Small File Reading',
        );
      }

      // For large files, use streaming to prevent ANR
      final stream = file.openRead();
      final chunks = <List<int>>[];
      int totalRead = 0;

      await for (final chunk in stream) {
        chunks.add(chunk);
        totalRead += chunk.length;

        // Update progress (cast to double for calculation)
        onProgress((totalRead / fileSize) * 0.3); // 30% for reading

        // Small delay every 5 chunks to prevent UI blocking
        if (chunks.length % 5 == 0) {
          await Future.delayed(const Duration(milliseconds: 10));
        }

        // Safety check to prevent infinite loop
        if (totalRead >= fileSize) break;
      }

      // Combine chunks efficiently
      final totalBytes = chunks.fold<int>(
        0,
        (sum, chunk) => sum + chunk.length,
      );
      final result = Uint8List(totalBytes);
      int offset = 0;

      for (final chunk in chunks) {
        result.setRange(offset, offset + chunk.length, chunk);
        offset += chunk.length;
      }

      return result;
    } catch (e) {
      debugPrint('❌ Chunked file reading failed: $e');
      return null;
    }
  }

  // Dispose all resources
  void dispose() {
    for (final subscription in _uploadSubscriptions.values) {
      subscription.cancel();
    }
    _uploadSubscriptions.clear();

    for (final task in _uploadTasks.values) {
      task?.cancel();
    }
    _uploadTasks.clear();
  }
}
