import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_selector/file_selector.dart';
import '../models/upload_file_model.dart';
import '../services/file_upload_service.dart';
import '../providers/document_provider.dart';

/// Optimized Upload Provider to prevent ANR issues during file uploads
class OptimizedUploadProvider with ChangeNotifier {
  final FileUploadService _uploadService = FileUploadService();

  final List<UploadFileModel> _uploadQueue = [];
  bool _isUploading = false;
  int _maxConcurrentUploads =
      3; // Limit concurrent uploads to prevent overwhelming Firebase

  // Context for accessing other providers
  BuildContext? _context;

  // Current category for uploads
  String? _currentCategoryId;

  // Upload completion callback
  VoidCallback? _uploadCompletionCallback;

  // Upload progress tracking
  final Map<String, StreamSubscription> _progressSubscriptions = {};
  final Map<String, Timer> _timeoutTimers = {};

  List<UploadFileModel> get uploadQueue => List.unmodifiable(_uploadQueue);
  bool get isUploading => _isUploading;
  String? get currentCategoryId => _currentCategoryId;

  // Get upload statistics
  Map<String, int> get uploadStats {
    final pending = _uploadQueue
        .where((f) => f.status == UploadStatus.pending)
        .length;
    final uploading = _uploadQueue
        .where((f) => f.status == UploadStatus.uploading)
        .length;
    final completed = _uploadQueue
        .where((f) => f.status == UploadStatus.completed)
        .length;
    final failed = _uploadQueue
        .where((f) => f.status == UploadStatus.failed)
        .length;

    return {
      'pending': pending,
      'uploading': uploading,
      'completed': completed,
      'failed': failed,
      'total': _uploadQueue.length,
    };
  }

  // Get overall upload progress (0.0 to 1.0)
  double get overallProgress {
    if (_uploadQueue.isEmpty) return 0.0;

    double totalProgress = 0.0;
    for (final file in _uploadQueue) {
      switch (file.status) {
        case UploadStatus.completed:
          totalProgress += 1.0;
          break;
        case UploadStatus.uploading:
          totalProgress += file.progress;
          break;
        case UploadStatus.failed:
        case UploadStatus.pending:
        case UploadStatus.paused:
        case UploadStatus.cancelled:
          totalProgress += 0.0;
          break;
      }
    }

    return totalProgress / _uploadQueue.length;
  }

  /// Add multiple files with optimized validation
  Future<void> addFiles(List<XFile> files, {String? categoryId}) async {
    if (files.isEmpty) return;

    debugPrint('📁 Adding ${files.length} files to upload queue...');

    // Set category if provided
    if (categoryId != null) {
      _currentCategoryId = categoryId;
    }

    // Validate and process files in batches to prevent UI blocking
    final validatedFiles = await _validateFilesBatched(files);

    // Add validated files to queue
    for (final uploadFile in validatedFiles) {
      _uploadQueue.add(uploadFile);
    }

    notifyListeners();

    // Start upload process if not already running
    if (!_isUploading && validatedFiles.isNotEmpty) {
      _startOptimizedUploadProcess();
    }
  }

  /// Validate files in batches to prevent UI blocking
  Future<List<UploadFileModel>> _validateFilesBatched(List<XFile> files) async {
    const batchSize = 5;
    final validatedFiles = <UploadFileModel>[];

    for (int i = 0; i < files.length; i += batchSize) {
      final batch = files.skip(i).take(batchSize).toList();

      // Process batch with timeout
      final batchResults =
          await Future.wait(
            batch.map((file) => _validateSingleFile(file)),
            eagerError: false,
          ).timeout(
            const Duration(seconds: 30),
            onTimeout: () {
              debugPrint('⚠️ File validation timeout for batch');
              return <UploadFileModel?>[];
            },
          );

      // Add valid files
      for (final file in batchResults) {
        if (file != null) {
          validatedFiles.add(file);
        }
      }

      // Small delay between batches
      if (i + batchSize < files.length) {
        await Future.delayed(const Duration(milliseconds: 50));
      }
    }

    return validatedFiles;
  }

  /// Validate a single file
  Future<UploadFileModel?> _validateSingleFile(XFile file) async {
    try {
      // Check file size (max 15MB)
      const maxFileSize = 15 * 1024 * 1024;
      final fileSize = await file.length();

      if (fileSize > maxFileSize) {
        debugPrint('❌ File too large: ${file.name} ($fileSize bytes)');
        return null;
      }

      // Check file type
      final allowedExtensions = {
        'pdf',
        'doc',
        'docx',
        'pptx',
        'txt',
        'jpg',
        'jpeg',
        'png',
        'xlsx',
        'xls',
      };

      final extension = file.name.split('.').last.toLowerCase();
      if (!allowedExtensions.contains(extension)) {
        debugPrint('❌ Unsupported file type: ${file.name}');
        return null;
      }

      return UploadFileModel(
        id: _generateUploadId(),
        file: file,
        fileName: file.name,
        fileSize: fileSize,
        fileType: _getFileTypeFromExtension(extension),
        categoryId: _currentCategoryId,
        status: UploadStatus.pending,
        progress: 0.0,
      );
    } catch (e) {
      debugPrint('❌ File validation error for ${file.name}: $e');
      return null;
    }
  }

  /// Start optimized upload process with controlled concurrency
  Future<void> _startOptimizedUploadProcess() async {
    if (_isUploading) return;

    _isUploading = true;
    notifyListeners();

    try {
      // Check connectivity before starting
      final isConnected = await _uploadService.checkConnectivity();
      if (!isConnected) {
        _markAllPendingAsFailed('No internet connection');
        return;
      }

      final pendingFiles = _uploadQueue
          .where((file) => file.status == UploadStatus.pending)
          .toList();

      if (pendingFiles.isEmpty) {
        return;
      }

      debugPrint(
        '🚀 Starting optimized upload of ${pendingFiles.length} files...',
      );

      // Upload files with controlled concurrency
      await _uploadFilesWithConcurrencyControl(pendingFiles);
    } catch (e) {
      debugPrint('❌ Upload process error: $e');
    } finally {
      _isUploading = false;
      notifyListeners();

      // Call completion callback if all uploads are done
      _checkAndCallCompletionCallback();
    }
  }

  /// Upload files with controlled concurrency to prevent overwhelming Firebase
  Future<void> _uploadFilesWithConcurrencyControl(
    List<UploadFileModel> files,
  ) async {
    final semaphore = _Semaphore(_maxConcurrentUploads);

    // Create upload tasks for all files
    final uploadTasks = files
        .map(
          (file) => semaphore.acquire().then((_) async {
            try {
              await _uploadSingleFileOptimized(file);
            } finally {
              semaphore.release();
            }
          }),
        )
        .toList();

    // Wait for all uploads to complete
    await Future.wait(uploadTasks, eagerError: false);
  }

  /// Upload a single file with optimized error handling and timeouts
  Future<void> _uploadSingleFileOptimized(UploadFileModel file) async {
    const maxRetries = 2;
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        // Update status to uploading
        _updateFileStatus(file.id, UploadStatus.uploading);
        file.uploadStartTime = DateTime.now();

        // Set up timeout timer
        _timeoutTimers[file.id] = Timer(const Duration(minutes: 5), () {
          _updateFileStatus(file.id, UploadStatus.failed);
          _updateFileError(file.id, 'Upload timeout - please try again');
        });

        // Upload with progress tracking
        final downloadUrl = await _uploadService
            .uploadFile(
              file,
              onProgress: (progress) {
                _updateFileProgress(file.id, progress);
              },
            )
            .timeout(
              const Duration(minutes: 5),
              onTimeout: () => throw TimeoutException('Upload timeout'),
            );

        // Cancel timeout timer
        _timeoutTimers[file.id]?.cancel();
        _timeoutTimers.remove(file.id);

        // Update status to completed
        _updateFileStatus(file.id, UploadStatus.completed);
        _updateFileDownloadUrl(file.id, downloadUrl);
        file.uploadEndTime = DateTime.now();

        // Refresh document provider if context is available
        await _refreshDocumentProvider();

        debugPrint('✅ Upload completed: ${file.fileName}');
        return; // Success, exit retry loop
      } catch (e) {
        retryCount++;
        debugPrint(
          '❌ Upload attempt $retryCount failed for ${file.fileName}: $e',
        );

        // Cancel timeout timer
        _timeoutTimers[file.id]?.cancel();
        _timeoutTimers.remove(file.id);

        if (retryCount >= maxRetries) {
          // Final failure
          _updateFileStatus(file.id, UploadStatus.failed);
          _updateFileError(
            file.id,
            'Upload failed after $maxRetries attempts: ${e.toString()}',
          );
        } else {
          // Wait before retry
          await Future.delayed(Duration(seconds: retryCount * 2));
        }
      }
    }
  }

  /// Mark all pending files as failed
  void _markAllPendingAsFailed(String errorMessage) {
    final pendingFiles = _uploadQueue
        .where((file) => file.status == UploadStatus.pending)
        .toList();

    for (final file in pendingFiles) {
      _updateFileStatus(file.id, UploadStatus.failed);
      _updateFileError(file.id, errorMessage);
    }

    _isUploading = false;
    notifyListeners();
  }

  /// Update file status
  void _updateFileStatus(String fileId, UploadStatus status) {
    final index = _uploadQueue.indexWhere((file) => file.id == fileId);
    if (index != -1) {
      _uploadQueue[index] = _uploadQueue[index].copyWith(status: status);
      notifyListeners();
    }
  }

  /// Update file progress
  void _updateFileProgress(String fileId, double progress) {
    final index = _uploadQueue.indexWhere((file) => file.id == fileId);
    if (index != -1) {
      _uploadQueue[index] = _uploadQueue[index].copyWith(progress: progress);
      notifyListeners();
    }
  }

  /// Update file error message
  void _updateFileError(String fileId, String errorMessage) {
    final index = _uploadQueue.indexWhere((file) => file.id == fileId);
    if (index != -1) {
      _uploadQueue[index] = _uploadQueue[index].copyWith(
        errorMessage: errorMessage,
      );
      notifyListeners();
    }
  }

  /// Update file download URL
  void _updateFileDownloadUrl(String fileId, String downloadUrl) {
    final index = _uploadQueue.indexWhere((file) => file.id == fileId);
    if (index != -1) {
      _uploadQueue[index] = _uploadQueue[index].copyWith(
        downloadUrl: downloadUrl,
      );
      notifyListeners();
    }
  }

  /// Refresh document provider after successful upload
  Future<void> _refreshDocumentProvider() async {
    if (_context != null) {
      try {
        final documentProvider = Provider.of<DocumentProvider>(
          _context!,
          listen: false,
        );
        await documentProvider.loadDocuments();
      } catch (e) {
        debugPrint('⚠️ Failed to refresh document provider: $e');
      }
    }
  }

  /// Check and call completion callback
  void _checkAndCallCompletionCallback() {
    if (_uploadCompletionCallback != null &&
        _uploadQueue.isNotEmpty &&
        _uploadQueue.every(
          (file) =>
              file.status == UploadStatus.completed ||
              file.status == UploadStatus.failed,
        )) {
      _uploadCompletionCallback!();
    }
  }

  /// Generate unique upload ID
  String _generateUploadId() {
    return 'upload_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }

  /// Get file type from extension
  String _getFileTypeFromExtension(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return 'PDF';
      case 'doc':
      case 'docx':
        return 'Word Document';
      case 'ppt':
      case 'pptx':
        return 'PowerPoint';
      case 'xls':
      case 'xlsx':
        return 'Excel';
      case 'jpg':
      case 'jpeg':
      case 'png':
        return 'Image';
      case 'txt':
        return 'Text';
      default:
        return 'Document';
    }
  }

  // Public methods for external control
  void setContext(BuildContext context) => _context = context;
  void setCurrentCategory(String? categoryId) =>
      _currentCategoryId = categoryId;
  void setUploadCompletionCallback(VoidCallback? callback) =>
      _uploadCompletionCallback = callback;
  void setMaxConcurrentUploads(int max) =>
      _maxConcurrentUploads = max.clamp(1, 5);

  /// Clear completed uploads
  void clearCompletedUploads() {
    _uploadQueue.removeWhere((file) => file.status == UploadStatus.completed);
    notifyListeners();
  }

  /// Clear all uploads and reset
  void clearAllAndReset() {
    // Cancel all timers
    for (final timer in _timeoutTimers.values) {
      timer.cancel();
    }
    _timeoutTimers.clear();

    // Cancel all subscriptions
    for (final subscription in _progressSubscriptions.values) {
      subscription.cancel();
    }
    _progressSubscriptions.clear();

    _uploadQueue.clear();
    _isUploading = false;
    _uploadCompletionCallback = null;
    notifyListeners();
  }

  @override
  void dispose() {
    clearAllAndReset();
    super.dispose();
  }
}

/// Semaphore implementation for controlling concurrency
class _Semaphore {
  final int maxCount;
  int _currentCount;
  final List<Completer<void>> _waitQueue = [];

  _Semaphore(this.maxCount) : _currentCount = maxCount;

  Future<void> acquire() async {
    if (_currentCount > 0) {
      _currentCount--;
      return;
    }

    final completer = Completer<void>();
    _waitQueue.add(completer);
    return completer.future;
  }

  void release() {
    if (_waitQueue.isNotEmpty) {
      final completer = _waitQueue.removeAt(0);
      completer.complete();
    } else {
      _currentCount++;
    }
  }
}
