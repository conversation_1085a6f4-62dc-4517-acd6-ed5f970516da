import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_selector/file_selector.dart';
import '../models/upload_file_model.dart';
import '../models/document_model.dart';
import '../services/file_upload_service.dart';

import 'document_provider.dart';
import 'auth_provider.dart';

class UploadProvider with ChangeNotifier {
  final FileUploadService _uploadService = FileUploadService();

  final List<UploadFileModel> _uploadQueue = [];
  bool _isUploading = false;

  // Context for accessing other providers
  BuildContext? _context;

  // Current category for uploads (set when uploading to specific category)
  String? _currentCategoryId;

  // Upload completion callback
  VoidCallback? _uploadCompletionCallback;

  List<UploadFileModel> get uploadQueue => List.unmodifiable(_uploadQueue);
  bool get isUploading => _isUploading;

  // Statistics
  int get totalFiles => _uploadQueue.length;
  double get averageProgress {
    if (_uploadQueue.isEmpty) return 0.0;
    final totalProgress = _uploadQueue.fold<double>(
      0.0,
      (sum, file) => sum + file.progress,
    );
    return totalProgress / _uploadQueue.length;
  }

  int get completedFiles => _uploadQueue
      .where((file) => file.status == UploadStatus.completed)
      .length;

  int get failedFiles =>
      _uploadQueue.where((file) => file.status == UploadStatus.failed).length;

  // Check if there are successful uploads
  bool get hasSuccessfulUploads => completedFiles > 0;

  // Security and validation statistics
  int get securityRejectedFiles => _uploadQueue
      .where(
        (file) =>
            file.status == UploadStatus.failed &&
            (file.errorMessage?.contains('security') == true ||
                file.errorMessage?.contains('dangerous') == true ||
                file.errorMessage?.contains('suspicious') == true),
      )
      .length;

  int get validationFailedFiles => _uploadQueue
      .where(
        (file) =>
            file.status == UploadStatus.failed &&
            file.errorMessage?.contains('validation') == true,
      )
      .length;

  // Add files to upload queue with comprehensive validation
  Future<void> addFiles(List<XFile> files, {String? categoryId}) async {
    // Set current category for this upload session
    _currentCategoryId = categoryId;

    for (final file in files) {
      try {
        // Simple file validation
        final isValid = await _validateFile(file);

        final uploadFile = UploadFileModel.fromXFile(
          file,
          categoryId: categoryId,
        );

        if (!isValid) {
          // File failed validation - add as failed
          final failedFile = uploadFile.copyWith(
            status: UploadStatus.failed,
            errorMessage:
                'File validation failed - unsupported file type or too large',
          );
          _uploadQueue.add(failedFile);
        } else {
          // File passed validation - get file size
          final bytes = await file.readAsBytes();
          final updatedFile = uploadFile.copyWith(fileSize: bytes.length);
          _uploadQueue.add(updatedFile);
        }
      } catch (e) {
        // Add as failed file if validation throws exception
        final uploadFile = UploadFileModel.fromXFile(
          file,
          categoryId: categoryId,
        );
        final failedFile = uploadFile.copyWith(
          status: UploadStatus.failed,
          errorMessage: 'Validation error: ${e.toString()}',
        );
        _uploadQueue.add(failedFile);
      }
    }

    notifyListeners();

    // Start upload process if not already running
    if (!_isUploading) {
      _startUploadProcess();
    }
  }

  // Add single file
  Future<void> addFile(XFile file, {String? categoryId}) async {
    await addFiles([file], categoryId: categoryId);
  }

  // Start upload process with improved multiple file handling
  Future<void> _startUploadProcess() async {
    if (_isUploading) return;

    _isUploading = true;
    notifyListeners();

    // Check Firebase connectivity before starting uploads
    final isConnected = await _uploadService.checkConnectivity();
    if (!isConnected) {
      // Mark all pending files as failed due to connectivity
      final pendingFiles = _uploadQueue
          .where((file) => file.status == UploadStatus.pending)
          .toList();

      for (final file in pendingFiles) {
        _updateFileStatus(file.id, UploadStatus.failed);
        _updateFileError(
          file.id,
          'No internet connection - please check your network and try again',
        );
      }

      _isUploading = false;
      notifyListeners();
      return;
    }

    final pendingFiles = _uploadQueue
        .where((file) => file.status == UploadStatus.pending)
        .toList();

    // Improved upload strategy for multiple files
    if (pendingFiles.length <= 3) {
      // For small batches, upload concurrently for better performance
      await _uploadFilesConcurrently(pendingFiles);
    } else {
      // For larger batches, use controlled sequential upload
      await _uploadFilesSequentially(pendingFiles);
    }

    _isUploading = false;
    notifyListeners();

    // Call completion callback if all uploads are done
    if (_uploadCompletionCallback != null &&
        _uploadQueue.isNotEmpty &&
        _uploadQueue.every(
          (file) =>
              file.status == UploadStatus.completed ||
              file.status == UploadStatus.failed,
        )) {
      _uploadCompletionCallback!();
    }
  }

  // Upload files concurrently for better performance (small batches)
  Future<void> _uploadFilesConcurrently(List<UploadFileModel> files) async {
    final uploadFutures = files.map(
      (file) => _uploadFileWithErrorHandling(file),
    );
    await Future.wait(uploadFutures);
  }

  // Upload files sequentially with better error handling (large batches)
  Future<void> _uploadFilesSequentially(List<UploadFileModel> files) async {
    for (final file in files) {
      try {
        await _uploadFileWithErrorHandling(file);
        // Shorter delay for better user experience
        await Future.delayed(const Duration(milliseconds: 200));
      } catch (e) {
        // Continue with next file even if one fails
        continue;
      }
    }
  }

  // Wrapper for upload with better error handling
  Future<void> _uploadFileWithErrorHandling(UploadFileModel file) async {
    try {
      await _uploadFile(file);
    } catch (e) {
      // Error is already handled in _uploadFile, just continue
    }
  }

  // Upload individual file with improved retry mechanism
  Future<void> _uploadFile(UploadFileModel file) async {
    const maxRetries = 2; // Reduced retries for faster failure detection
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        // Update status to uploading
        _updateFileStatus(file.id, UploadStatus.uploading);
        file.uploadStartTime = DateTime.now();

        // Reduced AI processing simulation for better performance
        if (Random().nextDouble() < 0.3) {
          // 30% chance instead of 50%
          _updateFileAiProcessing(file.id, true);
        }

        // Upload with progress tracking and timeout
        final downloadUrl = await _uploadService
            .uploadFile(
              file,
              onProgress: (progress) {
                _updateFileProgress(file.id, progress);
              },
            )
            .timeout(
              const Duration(
                minutes: 5,
              ), // Reduced timeout for faster failure detection
              onTimeout: () {
                throw Exception(
                  'Upload timeout - file too large or slow connection',
                );
              },
            );

        // Store download URL in file model
        file.downloadUrl = downloadUrl;

        // Mark as completed
        _updateFileStatus(file.id, UploadStatus.completed);
        file.uploadEndTime = DateTime.now();
        _updateFileAiProcessing(file.id, false);

        // Add to DocumentProvider
        _addToDocumentProvider(file, categoryId: _currentCategoryId);

        // Success - break out of retry loop
        break;
      } catch (e) {
        retryCount++;
        _updateFileAiProcessing(file.id, false);

        // Check if this is the last retry
        if (retryCount >= maxRetries) {
          _updateFileStatus(file.id, UploadStatus.failed);

          // Provide user-friendly error messages
          String errorMessage = _getErrorMessage(e.toString());
          _updateFileError(file.id, errorMessage);
          break; // Exit retry loop
        } else {
          // Shorter wait before retrying for better user experience
          await Future.delayed(Duration(seconds: retryCount));
        }
      }
    }
  }

  // Extract error message logic to separate method for cleaner code
  String _getErrorMessage(String errorString) {
    if (errorString.contains('File size exceeds') ||
        errorString.contains('15MB')) {
      return 'File terlalu besar (maksimal 15MB)';
    } else if (errorString.contains('cancelled') ||
        errorString.contains('canceled')) {
      return 'Upload dibatalkan - silakan coba lagi';
    } else if (errorString.contains('Authentication required') ||
        errorString.contains('unauthenticated')) {
      return 'Silakan login ulang dan coba lagi';
    } else if (errorString.contains('network') ||
        errorString.contains('Network') ||
        errorString.contains('retry-limit-exceeded')) {
      return 'Error jaringan - periksa koneksi internet';
    } else if (errorString.contains('permission') ||
        errorString.contains('Permission') ||
        errorString.contains('unauthorized')) {
      return 'Akses ditolak - periksa hak akses';
    } else if (errorString.contains('StorageException') ||
        errorString.contains('storage')) {
      return 'Error penyimpanan - silakan coba lagi';
    } else if (errorString.contains('timeout') ||
        errorString.contains('Timeout')) {
      return 'Upload timeout - coba file yang lebih kecil';
    } else if (errorString.contains('quota') || errorString.contains('Quota')) {
      return 'Kuota penyimpanan habis';
    } else if (errorString.contains('validation') ||
        errorString.contains('security')) {
      return 'Validasi file gagal - periksa jenis file';
    } else if (errorString.contains('corrupted') ||
        errorString.contains('checksum')) {
      return 'File rusak saat upload - silakan coba lagi';
    } else {
      return 'Upload gagal - silakan coba lagi';
    }
  }

  // Pause upload
  void pauseUpload(String fileId) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      final file = _uploadQueue[fileIndex];
      if (file.status == UploadStatus.uploading) {
        _updateFileStatus(fileId, UploadStatus.paused);
        _uploadService.pauseUpload(fileId);
      } else if (file.status == UploadStatus.paused) {
        _updateFileStatus(fileId, UploadStatus.uploading);
        _uploadService.resumeUpload(fileId);
      }
    }
  }

  // Cancel upload
  void cancelUpload(String fileId) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadService.cancelUpload(fileId);
      _uploadQueue.removeAt(fileIndex);
      notifyListeners();
    }
  }

  // Retry failed upload
  Future<void> retryUpload(String fileId) async {
    final file = _uploadQueue.firstWhere((f) => f.id == fileId);
    if (file.status == UploadStatus.failed) {
      _updateFileStatus(fileId, UploadStatus.pending);
      _updateFileError(fileId, null);
      _updateFileProgress(fileId, 0.0);

      if (!_isUploading) {
        _startUploadProcess();
      }
    }
  }

  // Clear all uploads
  void clearAll() {
    // Cancel all active uploads
    for (final file in _uploadQueue) {
      if (file.status == UploadStatus.uploading) {
        _uploadService.cancelUpload(file.id);
      }
    }

    _uploadQueue.clear();
    _isUploading = false;
    notifyListeners();
  }

  // Reset upload state (for reusing upload screen)
  void resetUploadState() {
    clearAll();
    _currentCategoryId = null;
    notifyListeners();
  }

  // Check if upload can be restarted
  bool get canRestartUpload {
    return !_isUploading &&
        _uploadQueue.every(
          (file) =>
              file.status == UploadStatus.completed ||
              file.status == UploadStatus.failed ||
              file.status == UploadStatus.cancelled,
        );
  }

  // Simple file validation
  Future<bool> _validateFile(XFile file) async {
    try {
      // Check file extension
      final extension = file.name.split('.').last.toLowerCase();
      final allowedExtensions = [
        'pdf',
        'doc',
        'docx',
        'pptx',
        'txt',
        'jpg',
        'jpeg',
        'png',
        'xlsx',
        'xls',
      ];

      if (!allowedExtensions.contains(extension)) {
        return false;
      }

      // Check file size (max 10MB)
      final bytes = await file.readAsBytes();
      if (bytes.length > 10 * 1024 * 1024) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  // Helper methods to update file properties
  void _updateFileStatus(String fileId, UploadStatus status) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].status = status;
      notifyListeners();
    }
  }

  void _updateFileProgress(String fileId, double progress) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].progress = progress;
      notifyListeners();
    }
  }

  void _updateFileError(String fileId, String? error) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].errorMessage = error;
      notifyListeners();
    }
  }

  void _updateFileAiProcessing(String fileId, bool isProcessing) {
    final fileIndex = _uploadQueue.indexWhere((f) => f.id == fileId);
    if (fileIndex != -1) {
      _uploadQueue[fileIndex].isAiProcessing = isProcessing;
      notifyListeners();
    }
  }

  // Set context for accessing other providers
  void setContext(BuildContext context) {
    _context = context;
  }

  // Set current category for uploads
  void setCurrentCategory(String? categoryId) {
    _currentCategoryId = categoryId;
  }

  // Set upload completion callback
  void setUploadCompletionCallback(VoidCallback? callback) {
    _uploadCompletionCallback = callback;
  }

  // Clear completed uploads only (keep failed uploads for retry)
  void clearCompletedUploads() {
    _uploadQueue.removeWhere((file) => file.status == UploadStatus.completed);
    notifyListeners();
  }

  // Clear all uploads and reset to clean state
  void clearAllAndReset() {
    clearAll();
    _uploadCompletionCallback = null;
    notifyListeners();
  }

  // Add uploaded file to DocumentProvider
  void _addToDocumentProvider(UploadFileModel file, {String? categoryId}) {
    if (_context == null) return;

    try {
      // Get current user ID from auth provider
      String currentUserId = 'current_user';
      try {
        final authProvider = Provider.of<AuthProvider>(
          _context!,
          listen: false,
        );
        currentUserId = authProvider.currentUser?.id ?? 'current_user';
      } catch (e) {
        // Use default if auth provider not available
      }

      // Create document model
      final document = DocumentModel(
        id: file.id,
        fileName: file.fileName,
        fileSize: file.fileSize,
        fileType: file.fileType,
        filePath:
            file.downloadUrl ?? file.file.path, // Use download URL if available
        uploadedBy: currentUserId,
        uploadedAt: DateTime.now(),
        category: categoryId ?? 'uncategorized',
        status: 'active',
        permissions: [currentUserId],
        metadata: DocumentMetadata(
          description: 'Uploaded via mobile app',
          tags: _generateTags(file.fileName),
        ),
      );

      // Get DocumentProvider and add document
      final documentProvider = Provider.of<DocumentProvider>(
        _context!,
        listen: false,
      );

      if (categoryId != null) {
        // Add to specific category
        documentProvider.addDocumentToCategory(document, categoryId);
      } else {
        // Add to general documents
        documentProvider.addDocument(document);
      }

      // Trigger immediate UI refresh for home screen and other file tables
      documentProvider.forceRefresh();
    } catch (e) {
      // Handle error silently
    }
  }

  // Generate tags based on filename
  List<String> _generateTags(String fileName) {
    final tags = <String>[];
    final nameParts = fileName
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s]'), ' ')
        .split(' ')
        .where((part) => part.isNotEmpty && part.length > 2)
        .toList();

    tags.addAll(nameParts);

    // Add file type tag
    final extension = fileName.split('.').last.toLowerCase();
    tags.add(extension);

    return tags.take(10).toList(); // Limit to 10 tags
  }

  @override
  void dispose() {
    // Cancel all uploads when provider is disposed
    for (final file in _uploadQueue) {
      if (file.status == UploadStatus.uploading) {
        _uploadService.cancelUpload(file.id);
      }
    }
    super.dispose();
  }
}
