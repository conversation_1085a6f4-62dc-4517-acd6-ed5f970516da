{"name": "simdoc-db-seeder", "version": "1.0.0", "description": "Database seeder untuk aplikasi SIMDOC Firebase Firestore", "main": "seed-all.js", "scripts": {"test": "node test-connection.js", "seed:all": "node seed-all.js", "seed:users": "node users.js", "seed:categories": "node categories.js", "seed:documents": "node documents.js", "seed:activities": "node activities.js", "verify": "node verify-data.js", "cleanup": "node cleanup.js"}, "dependencies": {"firebase-admin": "^12.0.0", "bcryptjs": "^2.4.3"}, "keywords": ["firebase", "firestore", "seeder", "simdoc"], "author": "SIMDOC Team", "license": "MIT"}