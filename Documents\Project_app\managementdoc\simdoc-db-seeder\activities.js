const { db, COLLECTIONS, generateTimestamp, generateId } = require('./config');

// Sample activities data
const activitiesData = [
  {
    id: 'act-001',
    userId: 'admin-001',
    action: 'login',
    resource: 'system',
    timestamp: generateTimestamp(1),
    details: {
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'Jakarta, Indonesia'
    }
  },
  {
    id: 'act-002',
    userId: 'user-001',
    action: 'upload',
    resource: 'document',
    timestamp: generateTimestamp(15),
    details: {
      documentId: 'doc-001',
      fileName: 'Surat_Masuk_001_2024.pdf',
      fileSize: 245760,
      category: 'cat-001'
    }
  },
  {
    id: 'act-003',
    userId: 'admin-001',
    action: 'approve',
    resource: 'document',
    timestamp: generateTimestamp(14),
    details: {
      documentId: 'doc-001',
      fileName: 'Surat_Masuk_001_2024.pdf',
      previousStatus: 'pending',
      newStatus: 'approved'
    }
  },
  {
    id: 'act-004',
    userId: 'admin-001',
    action: 'create_user',
    resource: 'user',
    timestamp: generateTimestamp(25),
    details: {
      targetUserId: 'user-001',
      userEmail: '<EMAIL>',
      userRole: 'user'
    }
  },
  {
    id: 'act-005',
    userId: 'user-002',
    action: 'upload',
    resource: 'document',
    timestamp: generateTimestamp(10),
    details: {
      documentId: 'doc-003',
      fileName: 'Notulen_Rapat_Koordinasi_Jan2024.docx',
      fileSize: 128000,
      category: 'cat-003'
    }
  },
  {
    id: 'act-006',
    userId: 'user-001',
    action: 'download',
    resource: 'document',
    timestamp: generateTimestamp(9),
    details: {
      documentId: 'doc-002',
      fileName: 'SK_Pengangkatan_Pegawai_2024.pdf'
    }
  },
  {
    id: 'act-007',
    userId: 'admin-001',
    action: 'create_category',
    resource: 'category',
    timestamp: generateTimestamp(30),
    details: {
      categoryId: 'cat-001',
      categoryName: 'Surat Masuk'
    }
  },
  {
    id: 'act-008',
    userId: 'user-003',
    action: 'login',
    resource: 'system',
    timestamp: generateTimestamp(5),
    details: {
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      location: 'Bandung, Indonesia'
    }
  },
  {
    id: 'act-009',
    userId: 'user-001',
    action: 'upload',
    resource: 'document',
    timestamp: generateTimestamp(8),
    details: {
      documentId: 'doc-004',
      fileName: 'Laporan_Evaluasi_Q4_2023.xlsx',
      fileSize: 1048576,
      category: 'cat-004'
    }
  },
  {
    id: 'act-010',
    userId: 'admin-001',
    action: 'approve',
    resource: 'document',
    timestamp: generateTimestamp(7),
    details: {
      documentId: 'doc-004',
      fileName: 'Laporan_Evaluasi_Q4_2023.xlsx',
      previousStatus: 'pending',
      newStatus: 'approved'
    }
  },
  {
    id: 'act-011',
    userId: 'user-003',
    action: 'upload',
    resource: 'document',
    timestamp: generateTimestamp(6),
    details: {
      documentId: 'doc-005',
      fileName: 'Surat_Keluar_Undangan_Seminar.pdf',
      fileSize: 320000,
      category: 'cat-005'
    }
  },
  {
    id: 'act-012',
    userId: 'admin-001',
    action: 'reject',
    resource: 'document',
    timestamp: generateTimestamp(1),
    details: {
      documentId: 'doc-008',
      fileName: 'Surat_Masuk_Komplain_Layanan.pdf',
      previousStatus: 'pending',
      newStatus: 'rejected',
      reason: 'Dokumen tidak sesuai format yang ditentukan'
    }
  },
  {
    id: 'act-013',
    userId: 'user-002',
    action: 'login',
    resource: 'system',
    timestamp: generateTimestamp(3),
    details: {
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      location: 'Surabaya, Indonesia'
    }
  },
  {
    id: 'act-014',
    userId: 'user-002',
    action: 'upload',
    resource: 'document',
    timestamp: generateTimestamp(4),
    details: {
      documentId: 'doc-006',
      fileName: 'Proposal_Workshop_Teknologi.pdf',
      fileSize: 768000,
      category: 'cat-006'
    }
  },
  {
    id: 'act-015',
    userId: 'admin-001',
    action: 'update_category',
    resource: 'category',
    timestamp: generateTimestamp(10),
    details: {
      categoryId: 'cat-008',
      categoryName: 'Arsip Lama',
      changes: {
        isActive: false
      }
    }
  }
];

async function seedActivities() {
  console.log('🚀 Starting activities seeding...');
  
  try {
    const batch = db.batch();
    
    for (const activity of activitiesData) {
      const activityRef = db.collection(COLLECTIONS.ACTIVITIES).doc(activity.id);
      const { id, ...activityData } = activity;
      batch.set(activityRef, activityData);
    }
    
    await batch.commit();
    console.log('✅ Activities collection seeded successfully!');
    console.log(`📊 Total activities created: ${activitiesData.length}`);
    
    // Display activities summary by action
    const actionCount = activitiesData.reduce((acc, activity) => {
      acc[activity.action] = (acc[activity.action] || 0) + 1;
      return acc;
    }, {});
    
    console.log('\n📋 Activities Summary by Action:');
    Object.entries(actionCount).forEach(([action, count]) => {
      console.log(`  📝 ${action}: ${count} activities`);
    });
    
  } catch (error) {
    console.error('❌ Error seeding activities:', error);
  }
}

// Run if called directly
if (require.main === module) {
  seedActivities().then(() => {
    console.log('🎉 Activities seeding completed!');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Activities seeding failed:', error);
    process.exit(1);
  });
}

module.exports = { seedActivities, activitiesData };
