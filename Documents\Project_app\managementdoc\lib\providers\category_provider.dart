import 'package:flutter/material.dart';
import '../models/category_model.dart';
import '../core/services/category_service.dart';

class CategoryProvider extends ChangeNotifier {
  final CategoryService _categoryService = CategoryService();
  List<CategoryModel> _categories = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<CategoryModel> get categories => _categories;
  List<CategoryModel> get activeCategories =>
      _categories.where((c) => c.isActive).toList();
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Load categories
  Future<void> loadCategories() async {
    _setLoading(true);
    _clearError();

    try {
      // Try to load from Firebase first
      try {
        _categories = await _categoryService.getAllCategories();
      } catch (firebaseError) {
        // If Firebase fails, start with empty categories
        _categories = [];
        _setError('Failed to load categories: $firebaseError');
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Add category
  Future<void> addCategory(CategoryModel category) async {
    try {
      // Add to Firebase first
      final categoryId = await _categoryService.addCategory(category);

      // Update local list with the new ID
      final updatedCategory = category.copyWith(id: categoryId);
      _categories.insert(0, updatedCategory);

      // Initialize empty category in DocumentProvider
      _initializeEmptyCategory(categoryId);

      notifyListeners();
    } catch (e) {
      // If Firebase fails, add locally only
      _categories.insert(0, category);

      // Initialize empty category in DocumentProvider
      _initializeEmptyCategory(category.id);

      notifyListeners();
      rethrow;
    }
  }

  // Initialize empty category in DocumentProvider
  void _initializeEmptyCategory(String categoryId) {
    // This will be called by DocumentProvider when needed
    // We don't need to import DocumentProvider here to avoid circular dependency
  }

  // Update category
  Future<void> updateCategory(CategoryModel category) async {
    try {
      // Update in Firebase first
      await _categoryService.updateCategory(category.id, category);

      // Update local list
      int index = _categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _categories[index] = category;
        notifyListeners();
      }
    } catch (e) {
      // If Firebase fails, update locally only
      int index = _categories.indexWhere((c) => c.id == category.id);
      if (index != -1) {
        _categories[index] = category;
        notifyListeners();
      }
      rethrow;
    }
  }

  // Remove category
  Future<void> removeCategory(String categoryId) async {
    try {
      // Remove from Firebase first
      await _categoryService.deleteCategory(categoryId);

      // Remove from local list
      _categories.removeWhere((c) => c.id == categoryId);
      notifyListeners();
    } catch (e) {
      // If Firebase fails, remove locally only
      _categories.removeWhere((c) => c.id == categoryId);
      notifyListeners();
      rethrow;
    }
  }

  // Toggle category status
  void toggleCategoryStatus(String categoryId) {
    int index = _categories.indexWhere((c) => c.id == categoryId);
    if (index != -1) {
      _categories[index] = _categories[index].copyWith(
        isActive: !_categories[index].isActive,
      );
      notifyListeners();
    }
  }

  // Get category by ID
  CategoryModel? getCategoryById(String categoryId) {
    try {
      return _categories.firstWhere((category) => category.id == categoryId);
    } catch (e) {
      return null;
    }
  }

  // Get category by name
  CategoryModel? getCategoryByName(String name) {
    try {
      return _categories.firstWhere((category) => category.name == name);
    } catch (e) {
      return null;
    }
  }

  // Get categories that user has access to
  List<CategoryModel> getCategoriesForUser(String userId) {
    return _categories.where((category) {
      return category.isActive &&
          (category.permissions.isEmpty || category.hasPermission(userId));
    }).toList();
  }

  // Get total categories count
  int get totalCategoriesCount {
    return _categories.length;
  }

  // Get active categories count
  int get activeCategoriesCount {
    return _categories.where((category) => category.isActive).length;
  }

  // Get inactive categories count
  int get inactiveCategoriesCount {
    return _categories.where((category) => !category.isActive).length;
  }

  // Search categories
  List<CategoryModel> searchCategories(String query) {
    if (query.isEmpty) return _categories;

    return _categories.where((category) {
      return category.name.toLowerCase().contains(query.toLowerCase()) ||
          category.description.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // Refresh categories
  Future<void> refreshCategories() async {
    await loadCategories();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear error manually
  void clearError() {
    _clearError();
  }
}
